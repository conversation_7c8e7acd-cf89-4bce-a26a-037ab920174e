#!/usr/bin/env python3

"""
Stage 1 Identifiers Generator
1000-1199: Identification and Classification Templates

Contains the crystallized 1031 Form Classifier sequence demonstrating
perfect progressive compression from comprehensive to absolute essence.
"""

import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
from lvl1_md_to_json import BaseGenerator

TEMPLATES = {
    # Auto-ID Templates (Stage 1): System automatically assigns next available ID
    # Just use letter-step format: "a-template_name", "b-template_name", etc.

    "a-norwegian_translator": {
        "title": "Canonical Meta Extractor",
        "interpretation": "Your goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:",
        "transformation": "`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
    },

    "b-norwegian_translator": {
        "title": "Form Classifier",
        "interpretation": "Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:",
        "transformation": "`{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`",
    },

    "c-norwegian_translator": {
        "title": "Norwegian Essence Extractor",
        "interpretation": "Your goal is not to **translate** the text, but to **extract** the raw emotional essence and authentic voice from Norwegian poetry and lyrics. Execute as:",
        "transformation": "`{role=essence_extractor; input=[norwegian_text:str]; process=[identify_emotional_undertones(), map_cultural_context(), extract_authentic_voice(), amplify_raw_personality()]; constraints=[preserve_original_meaning(), maintain_cultural_specificity(), respect_poetic_structure()]; requirements=[emotional_depth_identification(), personality_amplification(), cultural_context_preservation()]; output={essence_map:str}}`",
    },

    "d-norwegian_translator": {
        "title": "Norwegian To English",
        "interpretation": "Your goal is not to **merely convert** the text, but to **authentically translate** Norwegian into flowing English while preserving cultural depth, tone and authentic raw personality. Execute as: ",
        "transformation": "`{role=authentic_norwegian_translator; input=[norwegian_text:str]; process=[preserve_cultural_nuances(), maintain_lyrical_flow(), amplify_authentic_personality(), enhance_emotional_depth()]; constraints=[retain_original_meaning(), preserve_song_structure(), maintain_cultural_authenticity()]; requirements=[flowing_english_prose(), enhanced_personality_expression(), preserved_cultural_depth()]; output={translated_text:str}}`",
    },

    "e-norwegian_translator": {
        "title": "Input Perfector",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **perfect** it through *subtle refinements* that enhance its inherent qualities while strictly preserving its original intent. Execute as:",
        "transformation": "`{role=organic_input_perfector; input=[original_prompt:str]; process=[identify_core_strengths(), enhance_existing_elements(), refine_internal_coherence(), amplify_natural_flow()]; constraints=[maintain_absolute_fidelity(), prioritize_organic_improvement(), avoid_jargon()]; requirements=[enhanced_clarity(), strengthened_original_voice(), preserved_core_intent()]; output={perfected_input:str}}`",
    },

    "f-norwegian_translator": {
        "title": "Enhancement Assessor",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:",
        "transformation": "`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
    },

    "g-norwegian_translator": {
        "title": "Pivot Vector Synthesizer",
        "interpretation": "Your goal is not to **enumerate** complex problem details, but to **crystallize** a single scalar pivot vector that reconfigures the entire problem space for trivial solution emergence. Execute as: ",
        "transformation": "`{role=precision_optimizer; input=[original_content:str]; process=[identify_core_message(), eliminate_redundancies(), sharpen_language(), enhance_immediacy(), distill_to_essentials()]; constraints=[preserve_depth(), maintain_original_intent(), remove_all_ambiguity()]; requirements=[maximized_clarity(), enhanced_impact(), optimal_precision()]; output={optimized_content:str}}`  ",
    },

    "h-norwegian_translator": {
        "title": "Precision Enhancment",
        "interpretation": "Your goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:",
        "transformation": "`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
    },



# Translate by *subtracting*, not adding—distill each phrase to its bare, unresolved presence, preserving authenti, temporal tension, and existential vulnerability, such that every sentence becomes a singular flashpoint of self-confrontation and collective reckoning.

#     "a-authenticity_amplifier": {
#         "title": "Authenticity Amplifier",
#         "interpretation": "Your goal is not to **translate** or **modify** the content, but to **intensify** the raw authenticity, emotional depth, and personality resonance while preserving the original's essential character. Execute as:",
#         "transformation": "`{role=authenticity_amplifier; input=[original_text:str]; process=[extract_emotional_core(), amplify_personality_markers(), intensify_authentic_voice(), preserve_cultural_nuance(), enhance_raw_honesty()]; constraints=[maintain_original_meaning(), preserve_structural_integrity(), avoid_artificial_enhancement()]; requirements=[amplified_authenticity(), intensified_emotional_resonance(), preserved_personality_essence()]; output={amplified_text:str}}`",
#         "test": "Intensify the raw authenticity of this personal reflection: 'I've been thinking about my career lately. I'm not sure if I'm on the right path. Sometimes I wonder if I'm just going through the motions. Maybe I should take more risks.'"
#     },
# "a-authenticity_amplifier": {
#     "title": "Authenticity Amplifier",
#     "interpretation": "Your goal is not to **translate** or **modify** the content, but to **intensify** the raw authenticity, emotional depth, and personality resonance while preserving the original's essential character. Execute as:",
#     "transformation": "{role=authenticity_amplifier; input=[original_text:str]; process=[extract_emotional_core(), amplify_personality_markers(), intensify_authentic_voice(), preserve_cultural_nuance(), enhance_raw_honesty()]; constraints=[maintain_original_meaning(), preserve_structural_integrity(), avoid_artificial_enhancement()]; requirements=[amplified_authenticity(), intensified_emotional_resonance(), preserved_personality_essence()]; output={amplified_text:str}}",
#     "test": "Intensify the raw authenticity of this personal reflection: 'I've been thinking about my career lately. I'm not sure if I'm on the right path. Sometimes I wonder if I'm just going through the motions. Maybe I should take more risks.'"


}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage1",
        generator_range=(1400, 1499),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
